import { useState, useEffect, useCallback } from 'react'
import { getArea } from '@/apis'
import _ from 'lodash'

interface UseAreaDataOptions {
  /** 是否启用异步加载模式 */
  asyncMode?: boolean
  /** 初始加载的父级ID，默认为0（省级） */
  initialParentId?: number
}

export const useAreaData = (options: UseAreaDataOptions = {}) => {
  const { asyncMode = false, initialParentId = 4744 } = options
  const [areaOptions, setAreaOptions] = useState<Area.Option[]>([])
  const [loading, setLoading] = useState(false)

  /**
   * 判断是否为直辖市
   * @param id 地区ID
   * @returns boolean
   */
  const isMunicipality = useCallback((id: string | number): boolean => {
    const numId = typeof id === 'string' ? parseInt(id) : id
    // 直辖市ID：1-北京, 2-上海, 3-天津, 4-重庆
    return [1, 2, 3, 4].includes(numId)
  }, [])

  /**
   * 根据请求层级推断当前数据的层级
   * @param requestLevel 当前请求的层级（0开始计数）
   * @param parentId 父级ID，用于判断是否为直辖市
   * @returns 当前数据的层级：0-省级, 1-市级, 2-区级
   */
  const inferDataLevel = useCallback((requestLevel: number, parentId: number | null = null): number => {
    if (requestLevel === 0) {
      return 0 // 省级数据
    }

    // 如果父级是直辖市，跳过市级
    if (requestLevel === 1 && parentId && isMunicipality(parentId)) {
      return 2 // 直辖市的第二级是区级
    }

    return requestLevel // 普通情况：1-市级, 2-区级
  }, [isMunicipality])

  /**
   * 加载地区数据
   * @param parentId 父级ID，0或null表示加载省级数据
   * @param requestLevel 请求层级，用于推断返回数据的层级
   * @returns Promise<Area.Option[]>
   */
  const loadAreaData = useCallback(async (parentId: number | null = null, requestLevel: number = 0): Promise<Area.Option[]> => {
    setLoading(true)
    try {
      const fid = parentId || 4744
      const result = await getArea({ fid })

      if (result?.data?.list && Array.isArray(result.data.list)) {
        // 推断当前数据的层级
        const currentLevel = inferDataLevel(requestLevel, parentId)
        const sortList = result.data.list.sort((a, b) => a.id - b.id)
        return _.map(sortList, item => ({
          value: item.id.toString(),
          label: item.name,
          level: currentLevel,
          isLeaf: currentLevel >= 2 // 区级（level=2）为叶子节点
        }))
      }

      return []
    } catch (error) {
      console.error('Failed to load area data:', error)
      return []
    } finally {
      setLoading(false)
    }
  }, [inferDataLevel])

  /**
   * 异步加载函数，用于 Cascader 的 loadData 属性
   */
  const loadData = useCallback(async (selectedOptions: Area.Option[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1]
    const targetId = parseInt(targetOption.value)

    // 如果已经是叶子节点，不需要再加载
    if (targetOption.isLeaf) {
      return
    }

    // 根据当前节点的层级判断是否需要继续加载
    // 直辖市：省级(0) -> 区级(2)
    // 普通省份：省级(0) -> 市级(1) -> 区级(2)
    if (targetOption.level !== undefined && targetOption.level >= 2) {
      return // 区级不需要再加载
    }

    // 如果已经有子数据，不需要重复加载
    if (targetOption.children && targetOption.children.length > 0) {
      return
    }

    try {
      // 计算下一级的请求层级
      const nextRequestLevel = selectedOptions.length
      const childOptions = await loadAreaData(targetId, nextRequestLevel)
      targetOption.children = childOptions

      // 触发重新渲染
      setAreaOptions([...areaOptions])
    } catch (error) {
      console.error('Failed to load child area data:', error)
    }
  }, [loadAreaData, areaOptions])

  // 使用 Lodash 将扁平化的地区数据转换为标准 Option 格式
  const formatAreaData = useCallback((data: Area.AreaData[], requestLevel: number = 0, parentId: number | null = null): Area.Option[] => {
    if (_.isEmpty(data)) {
      return []
    }

    // 推断当前数据的层级
    const currentLevel = inferDataLevel(requestLevel, parentId)

    // 直接将扁平化数据转换为 Option 格式
    return _.map(data, item => {
      const option: Area.Option = {
        value: item.id.toString(),
        label: item.name,
        level: currentLevel,
        isLeaf: currentLevel >= 2
      }

      return option
    })
  }, [inferDataLevel])

  // 构建树状结构的函数
  const buildTreeFromFlatData = useCallback((flatData: Area.AreaData[]): Area.Option[] => {
    if (_.isEmpty(flatData)) {
      return []
    }

    // 使用 Lodash 按 parentId 分组
    const groupedByParent = _.groupBy(flatData, 'parentId')

    // 构建树状结构的递归函数
    const buildChildren = (parentId: number | null = null, currentLevel: number = 0): Area.Option[] => {
      const children = groupedByParent[parentId as any] || []

      return _.map(children, item => {
        const option: Area.Option = {
          value: item.id.toString(),
          label: item.name,
          level: currentLevel,
          isLeaf: currentLevel >= 2
        }

        // 递归构建子节点
        // 根据当前节点判断下一级的层级
        let nextLevel = currentLevel + 1
        if (currentLevel === 0 && isMunicipality(item.id)) {
          nextLevel = 2 // 直辖市从省级直接跳到区级
        }

        const childOptions = buildChildren(item.id, nextLevel)
        if (!_.isEmpty(childOptions)) {
          option.children = childOptions
          option.isLeaf = false // 有子节点就不是叶子节点
        }

        return option
      })
    }

    // 从根节点开始构建（parentId 为 null 或 undefined 的节点）
    return buildChildren(null, 0)
  }, [isMunicipality])

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      if (asyncMode) {
        // 异步模式：只加载初始的省级数据
        const provinceData = await loadAreaData(initialParentId, 0)
        setAreaOptions(provinceData)
      } else {
        // 同步模式：加载完整的树状数据（保持原有逻辑）
        setLoading(true)
        try {
          const result = await getArea({ fid: initialParentId })

          if (result?.data?.list && Array.isArray(result.data.list)) {
            // 检查数据是否包含 parentId，如果有则构建树状结构，否则使用扁平化格式
            const hasParentId = result.data.list.some((item: any) =>
              'parentId' in item && item.parentId !== undefined && item.parentId !== null
            )

            const formattedData = hasParentId
              ? buildTreeFromFlatData(result.data.list)
              : formatAreaData(result.data.list, 0, initialParentId)

            setAreaOptions(formattedData)
          } else {
            console.error('Invalid area data format:', result)
            setAreaOptions([])
          }
        } catch (error) {
          console.error('Failed to load area data:', error)
          setAreaOptions([])
        } finally {
          setLoading(false)
        }
      }
    }

    initializeData()
  }, [asyncMode, initialParentId, loadAreaData, buildTreeFromFlatData, formatAreaData])

  return {
    options: areaOptions,
    loading,
    loadData: asyncMode ? loadData : undefined,
    loadAreaData,
    refresh: () => {
      setAreaOptions([])
      // 重新初始化
      const initializeData = async () => {
        const provinceData = await loadAreaData(initialParentId, 0)
        setAreaOptions(provinceData)
      }
      initializeData()
    }
  }
}