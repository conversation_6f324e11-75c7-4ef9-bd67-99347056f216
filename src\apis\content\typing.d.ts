declare namespace Content {
  // 文章管理类型
  type ArticleBase = {
    id: number;
    title: string;
    likeCount: number;
    viewCount: number;
    favoriteCount: number;
    status: number;
    creator: string;
    creatorId: number;
    createdAt: string;
    updatedAt: string;
  };

  type ArticleItem = ArticleBase & {
    rejectReason: string | null;
    reviewer: string | null;
    reviewerId: number | null;
    reviewTime: string | null;
  };

  type ArticleDetail = ArticleBase & {
    content: string;
    imagesContexts: string;
    imgUrls: string[];
    rejectReason: string | null;
    reviewer: string | null;
    reviewerId: number | null;
    reviewTime: string | null;
    modifier: string | null;
  };

  type ArticleReviewParams = {
    articleId: number;
    status: number;
    rejectReason?: string;
  };

  type ArticleCancelReviewParams = {
    articleId: number;
    status: number;
    rejectReason?: string;
  };

  type ArticleDeleteParams = {
    articleId: number;
    status: number;
    rejectReason?: string;
  };

  type ArticleListParams = {
    title?: string;
    creator?: string;
    status?: number;
    createdAtStartDate?: string;
    createdAtEndDate?: string;
    reviewStartDate?: string;
    reviewEndDate?: string;
  } & Global.PageParams;

  // 案例管理类型
  type CaseBase = {
    id: number;
    title: string;
    categoryId: number;
    categoryName: string;
    status: number;
    viewCount: number;
    creator: string;
    creatorId: number;
    createdAt: string;
    updatedAt: string;
  };

  type CaseItem = CaseBase & {
    rejectReason: string | null;
    reviewer: string | null;
    reviewTime: string | null;
  };

  type CaseDetail = CaseBase & {
    content: string;
    imagesContexts: string;
    imgUrls: string[];
    rejectReason: string | null;
    reviewer: string | null;
    reviewTime: string | null;
    modifier: string | null;
  };

  type CaseReviewParams = {
    caseId: number;
    status: number;
    rejectReason?: string;
  };

  type CaseCancelReviewParams = {
    caseId: number;
    status: number;
    rejectReason?: string;
  };

  type CaseDeleteParams = {
    caseId: number;
    status: number;
    rejectReason?: string;
  };

  type CaseListParams = {
    title?: string;
    creator?: string;
    categoryId?: number;
    status?: number;
    createdAtStartDate?: string;
    createdAtEndDate?: string;
    reviewStartDate?: string;
    reviewEndDate?: string;
  } & Global.PageParams;

  // 动态管理类型
  type DynamicBase = {
    id: number;
    title: string;
    categoryId: number;
    categoryName: string;
    status: number;
    viewCount: number;
    creator: string;
    creatorId: number;
    createdAt: string;
    updatedAt: string;
  };

  type DynamicItem = DynamicBase & {
    rejectReason: string | null;
    reviewer: string | null;
    reviewTime: string | null;
  };

  type DynamicDetail = DynamicBase & {
    content: string;
    rejectReason: string | null;
    reviewer: string | null;
    reviewTime: string | null;
    modifier: string | null;
  };

  type DynamicReviewParams = {
    dynamicsId: number;
    status: number;
    rejectReason?: string;
  };

  type DynamicCancelReviewParams = {
    dynamicsId: number;
    status: number;
    rejectReason?: string;
  };

  type DynamicDeleteParams = {
    dynamicsId: number;
    status: number;
    rejectReason?: string;
  };

  type DynamicListParams = {
    title?: string;
    creator?: string;
    categoryId?: number;
    status?: number;
    createdAtStartDate?: string;
    createdAtEndDate?: string;
    reviewStartDate?: string;
    reviewEndDate?: string;
  } & Global.PageParams;

  // 公共类型
  type CategoryItem = {
    id: number;
    name: string;
  };
}
