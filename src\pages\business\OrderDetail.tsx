import { getEntrustOrderDetail, updateLawOrderStatus, updateLawOrderInfo, getCaseStageList, orderAllocation } from '@/apis/business'
import { Card, Descriptions, Divider, Form, Button, Tag, message, Modal } from 'antd'
import { ModalForm, ProFormText, ProFormDigit, ProFormTextArea, ProFormSelect, ProFormInstance } from '@ant-design/pro-components'
import { useParams, useNavigate } from 'react-router-dom'
import { useEffect, useState, useRef } from 'react'
import { ResultEnum } from '@/utils/enums/httpEnum'
import LawyerSelect from '@/components/LawyerSelect'
import CaseTypeSelect from '@/components/CaseTypeSelect'
import AreaCascader from '@/components/AreaCascader'

const OrderDetail: React.FC = () => {
    const { id } = useParams()
    const navigate = useNavigate()
    const [orderData, setOrderData] = useState<Business.EntrustOrderDetail | null>(null)
    const [loading, setLoading] = useState<boolean>(true)
    const [statusChangeVisible, setStatusChangeVisible] = useState(false)
    const [editVisible, setEditVisible] = useState(false) // 新增：控制编辑弹窗的显示状态
    const [allocateLawyerVisible, setAllocateLawyerVisible] = useState(false) // 控制分配律师弹窗的显示状态
    const [area, setArea] = useState<string[]>([]) // 地区选择状态
    const [caseStageList, setCaseStageList] = useState<Business.CaseStageItem[]>([]) // 案件阶段列表

    const [selectedLawyerId, setSelectedLawyerId] = useState<number>(0) // 分配律师弹窗中选择的律师ID
    const [caseTypeId, setCaseTypeId] = useState<number>(0)
    const [form] = Form.useForm()
    const modalFormRef = useRef<ProFormInstance>()
    const statusFormRef = useRef<ProFormInstance>()
    const allocateLawyerFormRef = useRef<ProFormInstance>()

    // 获取订单详情
    const fetchDetail = async (orderId: string) => {
        try {
            const res = await getEntrustOrderDetail(Number(orderId))
            setOrderData(res.data)
        } finally {
            setLoading(false)
        }
    }

    // 获取案件阶段列表
    const fetchCaseStageList = async () => {
        try {
            const res = await getCaseStageList()
            if (res?.code === ResultEnum.SUCCESS) {
                setCaseStageList(res.data?.list || [])
            }
        } catch (error) {
            console.error('Failed to fetch case stage list', error)
        }
    }

    const handleAllocateLawyer = () => {
        setAllocateLawyerVisible(true)
        setSelectedLawyerId(0)
    }

    // 处理律师分配
    const handleLawyerAllocation = async (values: { lawyerId: number }) => {
        try {
            const res = await orderAllocation({
                orderId: Number(id),
                lawyerId: values.lawyerId
            })
            if (res?.code === ResultEnum.SUCCESS) {
                message.success('律师分配成功，订单状态已变更为待签约')
                setAllocateLawyerVisible(false)
                setSelectedLawyerId(0)
                id && fetchDetail(id) // 刷新数据
                return Promise.resolve()
            }
            return Promise.reject()
        } catch (error) {
            console.error('律师分配失败')
            return Promise.reject()
        }
    }

    // 处理状态更新
    const handleStatusUpdate = async (newStatus: number) => {
        try {
            const res = await updateLawOrderStatus({
                orderId: Number(id),
                status: newStatus
            })
            if (res?.code === ResultEnum.SUCCESS) {
                message.success('状态更新成功')
                id && fetchDetail(id)
                return Promise.resolve()
            }
            return Promise.reject()
        } catch (error) {
            message.error('状态更新失败')
            return Promise.reject()
        }
    }

    // 新增：处理订单信息修改
    const handleEditOrderInfo = async (values: any) => {
        try {
            // 处理地区数据
            let areaParams = {
                province: '',
                city: '',
                district: ''
            }

            if (area && Array.isArray(area) && area.length > 0) {
                if (area.length === 3) {
                    // 三级结构：[省名, 市名, 区名]
                    areaParams = {
                        province: area[0] || '',
                        city: area[1] || '',
                        district: area[2] || ''
                    }
                } else if (area.length === 2) {
                    // 两级结构（直辖市）：[省名, 区名]
                    areaParams = {
                        province: area[0] || '',
                        city: area[0] || '', // 直辖市的市级与省级相同
                        district: area[1] || ''
                    }
                } else if (area.length === 1) {
                    // 只选择了省级
                    areaParams = {
                        province: area[0] || '',
                        city: '',
                        district: ''
                    }
                }
            }

            const updateParams: Business.UpdateLawOrderInfoParams = {
                orderId: Number(id),
                caseTypeId: values.caseTypeId,
                caseStageId: values.caseStageId,
                amountInvolvedOfCase: Number(values.amountInvolvedOfCase),
                lawyerId: orderData?.lawyerId || 0, // 保持原有律师ID不变
                lawyerRequirements: values.lawyerRequirements || '',
                handlingAgency: values.handlingAgency || '',
                paymentAmount: Number(values.paymentAmount) || 0,
                province: areaParams.province,
                city: areaParams.city,
                district: areaParams.district
            }

            const res = await updateLawOrderInfo(updateParams)
            if (res?.code === ResultEnum.SUCCESS) {
                message.success('订单信息更新成功')
                setEditVisible(false)
                form.resetFields()
                setArea([])
                await fetchDetail(id!) // 刷新订单详情
            } else {
                message.error('订单信息更新失败')
            }
        } catch (error) {
            message.error('订单信息更新失败')
        }
    }

    // 处理编辑弹窗打开
    const handleEditOpen = () => {
        if (orderData) {
            // 初始化表单数据
            form.setFieldsValue({
                caseTypeId: orderData.caseTypeId,
                caseStageId: orderData.caseStageId,
                amountInvolvedOfCase: orderData.amountInvolvedOfCase,
                lawyerRequirements: orderData.lawyerRequirements || '',
                handlingAgency: orderData.handlingAgency || '',
                paymentAmount: orderData.paymentAmount || 0
            })

            // 初始化地区数据
            const areaValues = []
            if (orderData.province) {
                areaValues.push(orderData.province)
                if (orderData.city && orderData.city !== orderData.province) {
                    areaValues.push(orderData.city)
                }
                if (orderData.district) {
                    areaValues.push(orderData.district)
                }
            }
            setArea(areaValues)
            setCaseTypeId(orderData.caseTypeId)
        }
        setEditVisible(true)
    }

    useEffect(() => {
        id && fetchDetail(id)
        fetchCaseStageList()
    }, [id])
    // 订单状态映射（EntrustOrderDetail.orderStatus）
    const orderStatusMap = [
        <Tag key="-">-</Tag>,
        <Tag color="orange" key="orange" style={{ marginInlineEnd: 0 }}>待处理</Tag>,
        <Tag color="purple" key="purple" style={{ marginInlineEnd: 0 }}>待签约</Tag>,
        <Tag color="blue" key="blue" style={{ marginInlineEnd: 0 }}>跟进中</Tag>,
        <Tag color="green" key="green" style={{ marginInlineEnd: 0 }}>已完结</Tag>,
        <Tag color="red" key="red" style={{ marginInlineEnd: 0 }}>已作废</Tag>
    ]

    // 支付状态映射（EntrustOrderDetail.paymentStatus）
    const paymentStatusMap = [
        <Tag key="-">-</Tag>,
        <Tag color="orange" key="orange" style={{ marginInlineEnd: 0, width: 70, textAlign: 'center' }}>未支付</Tag>,
        <Tag color="green" key="green" style={{ marginInlineEnd: 0, width: 70, textAlign: 'center' }}>已支付</Tag>,
        <Tag color="red" key="red" style={{ marginInlineEnd: 0, width: 70, textAlign: 'center' }}>支付失败</Tag>
    ]

    return (
        <>
            <Card loading={loading}>
                {/* 基础信息 */}
                <Descriptions title="基础信息" bordered column={2}
                    styles={{
                        label: {
                            whiteSpace: 'nowrap',
                            width: '120px',
                            display: 'inline-block'
                        },
                        content: {
                            width: '50%'
                        }
                    }}
                >
                    <Descriptions.Item label="订单号">{orderData?.orderNo}</Descriptions.Item>
                    <Descriptions.Item label="客户名称">{orderData?.clientName}</Descriptions.Item>
                    <Descriptions.Item label="客户手机号">{orderData?.clientMobile}</Descriptions.Item>
                    <Descriptions.Item label="案件类型">{orderData?.caseTypeName}</Descriptions.Item>
                    <Descriptions.Item label="处理机构">{orderData?.handlingAgency}</Descriptions.Item>
                    <Descriptions.Item label="地区">
                        {(() => {
                            // 直辖市特殊处理：只显示省级和区级（支持带"市"和不带"市"的格式）
                            const municipalities = ['北京', '上海', '天津', '重庆']
                            const municipalitiesWithShi = ['北京市', '上海市', '天津市', '重庆市']
                            const isMunicipality = municipalities.includes(orderData?.province || '') || municipalitiesWithShi.includes(orderData?.province || '')

                            if (isMunicipality) {
                                return [orderData?.province, orderData?.district].filter(Boolean).join(' / ')
                            }
                            return [orderData?.province, orderData?.city, orderData?.district].filter(Boolean).join(' / ')

                        })()}
                    </Descriptions.Item>
                    <Descriptions.Item label="订单来源">
                        {(() => {
                            const sourceMap = {
                                'rescue': '黄金救援',
                                'optimal': '优配律师',
                                'customer': '客服'
                            }
                            return sourceMap[orderData?.source as keyof typeof sourceMap] || orderData?.source || '未知'
                        })()}
                    </Descriptions.Item>
                    <Descriptions.Item label="订单状态">
                        {orderData?.orderStatus && orderStatusMap[orderData.orderStatus]}
                    </Descriptions.Item>
                    <Descriptions.Item label="保证金">
                        {orderData?.paymentAmount ? `${orderData.paymentAmount}元` : '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="支付状态">
                        {orderData?.paymentStatus && paymentStatusMap[orderData.paymentStatus]}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">{orderData?.createdAt}</Descriptions.Item>
                </Descriptions>
                <Divider />

                {/* 案件详情 */}
                <Descriptions title="案件详情" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="案件阶段">
                        {orderData?.caseStage || '暂无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="涉及金额">
                        {orderData?.amountInvolvedOfCase ? `${orderData.amountInvolvedOfCase}元` : '暂无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="律师要求" span={2}>
                        {orderData?.lawyerRequirements || '暂无'}
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                {/* 律师信息 */}
                <Descriptions title="律师信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="律师姓名">
                        {orderData?.lawyerName || '未分配'}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建者">
                        {orderData?.creator}
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                {/* 审核信息 */}
                <Descriptions title="审核信息" bordered column={2} styles={{
                    label: {
                        whiteSpace: 'nowrap',
                        width: '120px',
                        display: 'inline-block'
                    },
                    content: {
                        width: '50%'
                    }
                }}>
                    <Descriptions.Item label="审核人">
                        {orderData?.reviewer || '暂无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="审核时间">
                        {orderData?.reviewTime || '暂无'}
                    </Descriptions.Item>
                </Descriptions>
            </Card>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
                {/* 新增：修改订单信息按钮 */}
                <Button type="primary" onClick={handleEditOpen} style={{ marginRight: 16 }}>
                    修改订单信息
                </Button>
                <Button
                    type="primary"
                    disabled={orderData?.isAllocation === 2}
                    onClick={handleAllocateLawyer}
                    style={{ marginRight: 16 }}
                >
                    {orderData?.isAllocation === 1 ? '分配律师' : '已分配律师'}
                </Button>

                {/* 根据订单状态显示操作按钮 */}
                {orderData?.orderStatus && [1, 2].includes(orderData.orderStatus) && (
                    <>
                        <Button
                            type="primary"
                            onClick={() => {
                                setStatusChangeVisible(true)
                            }}
                        >
                            更新订单状态
                        </Button>
                        <Button
                            danger
                            style={{ marginLeft: 16 }}
                            onClick={() => {
                                Modal.confirm({
                                    title: '确认取消订单',
                                    content: '取消后订单状态将变为已完结，此操作不可撤销，确定要取消吗？',
                                    okText: '确定取消',
                                    cancelText: '取消',
                                    okType: 'danger',
                                    onOk: () => handleStatusUpdate(5)
                                })
                            }}
                        >
                            取消订单
                        </Button>
                    </>
                )}
                <Button style={{ marginLeft: 16 }} onClick={() => navigate(-1)}>
                    返回
                </Button>
            </div>

            {/* 使用 ModalForm 重写编辑订单信息的弹窗 */}
            <ModalForm
                title="修改订单信息"
                open={editVisible}
                onOpenChange={setEditVisible}
                formRef={modalFormRef}
                onFinish={async (values) => {
                    await handleEditOrderInfo(values)
                    return true
                }}
                modalProps={{
                    destroyOnHidden: true,
                    onCancel: () => {
                        setArea([])
                    }
                }}
                layout="vertical"
                width={600}
                initialValues={{
                    caseTypeId: orderData?.caseTypeId,
                    caseStageId: orderData?.caseStageId,
                    amountInvolvedOfCase: orderData?.amountInvolvedOfCase,
                    lawyerRequirements: orderData?.lawyerRequirements || '',
                    handlingAgency: orderData?.handlingAgency || '',
                    paymentAmount: orderData?.paymentAmount || 0
                }}
            >
                {/* 案件类型选择 */}
                <div style={{ marginBottom: 24 }}>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                        案件类型 <span style={{ color: '#ff4d4f' }}>*</span>
                    </label>
                    <CaseTypeSelect
                        value={caseTypeId}
                        onChange={(value) => {
                            modalFormRef.current?.setFieldsValue({ caseTypeId: value })
                            setCaseTypeId(value)
                        }}
                    />
                </div>
                <ProFormText name="caseTypeId" hidden />
                <ProFormSelect
                    label="案件阶段"
                    name="caseStageId"
                    placeholder="请选择案件阶段"
                    options={caseStageList.map(item => ({
                        label: item.stageName,
                        value: item.id
                    }))}
                    rules={[{ required: true, message: '请选择案件阶段' }]}
                />

                {/* 地区选择 */}
                <div style={{ marginBottom: 24 }}>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                        地区 <span style={{ color: '#ff4d4f' }}>*</span>
                    </label>
                    <AreaCascader
                        placeholder="请选择地区"
                        asyncMode={true}
                        value={area}
                        onChange={setArea}
                    />
                </div>

                <ProFormText
                    label="处理机构"
                    name="handlingAgency"
                    rules={[{ required: true, message: '请输入处理机构' }]}
                    placeholder="请输入处理机构"
                />

                <ProFormDigit
                    label="涉及金额"
                    name="amountInvolvedOfCase"
                    rules={[{ required: true, message: '请输入涉及金额' }]}
                    placeholder="请输入涉及金额"
                    fieldProps={{
                        precision: 2,
                        min: 0,
                        formatter: (value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
                        parser: (value) => Number(value!.replace(/\$\s?|(,*)/g, '')),
                    }}
                />

                <ProFormDigit
                    label="保证金"
                    name="paymentAmount"
                    rules={[{ required: true, message: '请输入保证金' }]}
                    placeholder="请输入保证金"
                    fieldProps={{
                        precision: 2,
                        min: 0,
                        formatter: (value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
                        parser: (value) => Number(value!.replace(/\$\s?|(,*)/g, '')),
                    }}
                />

                <ProFormTextArea
                    label="律师要求"
                    name="lawyerRequirements"
                    placeholder="请输入律师要求"
                    fieldProps={{
                        rows: 4
                    }}
                />
            </ModalForm>
            {/* 使用 ModalForm 重写状态变更确认框 */}
            <ModalForm
                title="订单状态变更"
                open={statusChangeVisible}
                onOpenChange={setStatusChangeVisible}
                formRef={statusFormRef}
                onFinish={async (values) => {
                    if (values.newStatus) {
                        // 检查是否选择了已完结状态
                        if (values.newStatus === 4) {
                            Modal.warning({
                                title: '提示',
                                content: '订单状态不能直接更改为已完结，请使用取消订单功能',
                                okText: '确定'
                            })
                            return false
                        }
                        try {
                            await handleStatusUpdate(values.newStatus)
                            return true
                        } catch (error) {
                            return false
                        }
                    }
                    return false
                }}
                modalProps={{
                    destroyOnHidden: true,
                    width: 500
                }}
                layout="vertical"
                submitter={{
                    searchConfig: {
                        submitText: '确认变更',
                        resetText: '取消'
                    }
                }}
            >
                <ProFormSelect
                    label="选择新状态"
                    name="newStatus"
                    placeholder="请选择订单状态"
                    rules={[{ required: true, message: '请选择订单状态' }]}
                    options={[
                        { label: '已创建', value: 1, disabled: true },
                        { label: '待签约', value: 2 },
                        { label: '跟进中', value: 3 },
                        { label: '已完结', value: 4 }
                    ]}
                />
            </ModalForm>

            {/* 分配律师弹窗 */}
            <ModalForm
                title="分配律师"
                open={allocateLawyerVisible}
                onOpenChange={setAllocateLawyerVisible}
                formRef={allocateLawyerFormRef}
                onFinish={async (values) => {
                    console.log('🚀 ~ async ~ values:', values)
                    await handleLawyerAllocation(values as { lawyerId: number })
                    return true
                }}
                modalProps={{
                    destroyOnHidden: true,
                    width: 500
                }}
                layout="vertical"
                submitter={{
                    searchConfig: {
                        submitText: '确认分配',
                        resetText: '取消'
                    }
                }}
            >
                {/* 律师选择 */}
                <div style={{ marginBottom: 24 }}>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                        选择律师 <span style={{ color: '#ff4d4f' }}>*</span>
                    </label>
                    <LawyerSelect
                        value={selectedLawyerId}
                        onChange={(value) => {
                            allocateLawyerFormRef.current?.setFieldsValue({ lawyerId: value })
                            setSelectedLawyerId(value)
                        }}
                    />
                    <span style={{ marginTop: 12, display: 'block', color: '#ff0000ff', fontSize: 12 }}>分配后订单状态将变为待签约</span>
                </div>
                <ProFormText name="lawyerId" hidden rules={[{ required: true, message: '请选择律师' }]} />
            </ModalForm>
        </>
    )
}

export default OrderDetail


