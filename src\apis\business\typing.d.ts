declare namespace Business {
    // 委托订单基础类型
    type EntrustOrderBase = {
        id: number;
        orderNo: string;
        clientName: string;
        clientMobile: string;
        clientId: number;
        caseTypeId: number;
        caseTypeName: string;
        handlingAgency: string | null;
        province: string | null;
        city: string | null;
        district: string | null;
        orderStatus: number;
        paymentStatus: number;
        paymentAmount: number;
        lawyerId: number | null;
        lawyerName: string | null;
        creator: string;
        creatorId: number;
        createdAt: string;
        updatedAt: string;
        source: string // 订单来源：rescue=>黄金救援,optimal=>优配律师,customer=>客服
    };

    // 委托订单列表项类型
    type EntrustOrderItem = EntrustOrderBase & {
        caseStage: string | null;
    };

    // 委托订单详情类型
    type EntrustOrderDetail = EntrustOrderBase & {
        caseStage: string | null;
        caseStageId: number;
        amountInvolvedOfCase: number;
        lawyerRequirements: string | null;
        reviewer: string | null;
        reviewTime: string | null;
        isAllocation: number // 是否已分配，1否，2是，默认1
    };

    // 委托订单列表查询参数类型
    type GetEntrustOrderListParams = {
        page: number;
        pageSize: number;
        clientName?: string;
        paymentStatus?: number;
        caseStageId?: number;
        caseTypeId?: number;
        lawyerName?: string;
        province?: string;
        city?: string;
        district?: string;
        orderStatus?: number; // 订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废
        createdAtStartDate: string
        createdAtEndDate: string
    };

    // 更新委托订单参数类型
    type UpdateLawOrderInfoParams = {
        orderId: number;
        province: string;
        city: string;
        district: string;
        caseTypeId: number;
        caseStageId: number;
        amountInvolvedOfCase: number;
        lawyerId: number;
        lawyerRequirements: string;
        handlingAgency: string;
        paymentAmount: number;
    };

    // 律师基础类型
    type LawyerBase = {
        id: number;
        userId: number;
        name: string;
        phone: string;
        idCard: string;
        idCardFrontUrl: string;
        idCardBackUrl: string;
        articleNum: number; // 文章总数
        caseNum: number; // 案例总数
        createdAt: string;
    };

    // 律师列表项类型
    type LawyerItem = LawyerBase & {
        province: string | null;
        city: string | null;
        district: string | null;
        isGoldenRescue: number;
        licenseUrl: string;
        authStatus: number;
        rejectReason: string | null;
        reviewTime: string | null;
    };

    // 律师详情类型
    type LawyerDetail = LawyerBase & {
        id: number;
        userId: number;
        province: string;
        city: string;
        district: string;
        name: string;
        phone: string;
        personalProfile: string;
        figurePhotoUrl: string;
        lawFirm: string;
        lawFirmAddress: string;
        fieldIdStr: string;
        idCard: string;
        idCardFrontUrl: string;
        idCardBackUrl: string;
        isGoldenRescue: 1 | 2;
        licenseUrl: string;
        licenseNum: string;
        signature: string;
        authStatus: 0 | 1 | 2 | 3;
        rejectReason: string | null;
        creator: string;
        creatorId: number;
        articleNum: number;
        caseNum: number;
        lawyerLevel: 1 | 2 | 3 | 4 | 5;
        reviewer: string;
        modifier: string;
        reviewTime: string;
        createdAt: string;
        updatedAt: string;
    };

    // 律师列表查询参数类型
    type GetLawyerListParams = {
        page: number;
        pageSize: number;
        name?: string;
        lawyerLevel?: number;
        status?: number;
        province?: string;
        city?: string;
        district?: string;
    };

    // 律师审核参数类型
    type LawyerReviewParams = {
        lawyerId: number;
        status: number;
        rejectReason?: string;
    };

    // 创建订单参数类型
    type CreateOrderParams = {
        clientName: string;
        clientMobile: string;
        caseTypeId: number;
        caseStageId: number;
        amountInvolvedOfCase: number;
        lawyerRequirements?: string;
        province: string;
        city: string;
        district: string;
        lawyerId?: number;
        handlingAgency: string; // 办案机关
        paymentAmount: number; // 需要支付的金额(保证金)
    };

    // 委托阶段列表项类型
    type CaseStageItem = {
        stageName: string;
        id: number;
    };
}
